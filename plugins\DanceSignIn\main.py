import os, json, httpx, asyncio, time, traceback, random
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class DanceSignInPlugin(PluginBase):
    description = "唱舞全明星签到插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DanceSignIn"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/DanceSignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}


    def _load_config(self):
        """加载配置文件"""
        try:
            with open("plugins/DanceSignIn/config.toml", "rb") as f:
                config = tomllib.load(f)["DanceSignIn"]

            self.enable = config.get("enable", True)
            self.command = config.get("command", ["唱舞签到"])
            self.command_format = config.get("command-format", "唱舞签到 - 获取签到链接")


            # 限流配置
            rate_limit = config.get("rate_limit", {})
            self.cooldown = rate_limit.get("cooldown", 5)

            # 签到链接
            self.signin_link = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect"



        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置加载失败: {e}")
            self.enable = False



    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户限流，返回需要等待的时间"""
        current_time = time.time()
        last_time = self.user_last_request.get(user_wxid, 0)

        wait_time = self.cooldown - (current_time - last_time)
        if wait_time > 0:
            return wait_time

        self.user_last_request[user_wxid] = current_time
        return 0

    def _get_signin_link(self) -> str:
        """获取签到链接"""
        return self.signin_link

    def _get_card_info(self) -> tuple:
        """获取卡片信息"""
        title = "唱舞全明星"
        description = "点击进入签到页面，领取专属福利"
        thumb_url = "https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0"

        return title, description, thumb_url

    async def _send_miniprogram_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送小程序消息 - 派对邀请函"""
        try:
            # 艹，这个XML格式真TM复杂，老王我给你整理了一下
            xml = f'''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>派对邀请函-签到解锁6周年专属称号</title>
<des>唱舞星愿站</des>
<username />
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content />
<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
<lowurl />
<forwardflag>0</forwardflag>
<dataurl />
<lowdataurl />
<contentattr>0</contentattr>
<appattach>
    <attachid />
    <cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
    <cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
    <cdnthumblength>173353</cdnthumblength>
    <cdnthumbheight>576</cdnthumbheight>
    <cdnthumbwidth>720</cdnthumbwidth>
    <cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
    <aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
    <encryver>1</encryver>
    <fileext />
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo />
<androidsource>3</androidsource>
<sourceusername>gh_25eb09d7bc53@app</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<commenturl />
<thumburl />
<mediatagname />
<messageaction><![CDATA[]]></messageaction>
<messageext><![CDATA[]]></messageext>
<weappinfo>
    <pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>16</version>
    <type>2</type>
    <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
    <weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
    <shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
    <appservicetype>0</appservicetype>
    <secflagforsinglepagemode>0</secflagforsinglepagemode>
    <videopageinfo>
        <thumbwidth>720</thumbwidth>
        <thumbheight>576</thumbheight>
        <fromopensdk>0</fromopensdk>
    </videopageinfo>
    <wxaTradeCommentScore>0</wxaTradeCommentScore>
</weappinfo>
<statextstr />
<md5>69b76813fe0b2a04149aee01978e42f7</md5>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl></commenturl>'''

            # 发送小程序消息，这个SB消息类型是33
            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                33  # 小程序消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 小程序消息发送失败，这个憨批接口又抽风了")
                return False

            logger.info(f"[{self.plugin_name}] 小程序消息发送成功，msg_id: {new_msg_id}")
            return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 小程序消息发送异常，艹: {str(e)}")
            return False

    async def _process_signin_request(self, bot: WechatAPIClient, wxid: str, user_wxid: str) -> bool:
        """处理签到请求"""
        try:
            # 获取签到链接
            signin_link = self._get_signin_link()

            # 获取卡片信息
            title, description, thumb_url = self._get_card_info()

            # 发送链接卡片
            await bot.send_link_message(
                wxid,
                signin_link,
                title,
                description,
                thumb_url
            )

            # 等待一下再发送小程序消息，避免消息发送太快被吞
            await asyncio.sleep(1)

            # 发送小程序消息 - 派对邀请函
            miniprogram_success = await self._send_miniprogram_message(bot, wxid, user_wxid)

            if not miniprogram_success:
                logger.warning(f"[{self.plugin_name}] 小程序消息发送失败，但链接卡片已发送成功")

            return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送签到链接失败: {e}")
            return False

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = message["Content"].strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是签到命令
        if not any(content.startswith(cmd) for cmd in self.command):
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 处理签到请求
            success = await self._process_signin_request(bot, wxid, user_wxid)

            if not success:
                raise Exception("签到链接发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            await bot.send_at_message(wxid, "处理失败", [user_wxid])
