2025-08-03 00:06:58 | DEBUG | 收到消息: {'MsgId': 682320157, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="3f98377cf61c963da775ca3bdd469983" encryver="1" cdnthumbaeskey="3f98377cf61c963da775ca3bdd469983" cdnthumburl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" cdnthumblength="3932" cdnthumbheight="592" cdnthumbwidth="269" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" length="669824" cdnbigimgurl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" hdlength="565819" md5="9e490dcf99bfdb32a82a13e328baf22e" hevc_mid_size="85052" originsourcemd5="c72796111118bd9fabc2bb43924e03af">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMDAxMDEwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNmYwZTdmYjA1NTU2NmE5OWQ1\nNWE3YWVjNDU2OTJhZjQwNDRiZTJmZGFjYWI5NDRlMjgyNTA0NTJmYWI1MTU1YSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754150819, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>b356eb6af2faaa905c5a6d79dea76b5e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_bg5ubhTD|v1_H0Ra1Jmk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗在群聊中发了一张图片', 'NewMsgId': 4089417634120705834, 'MsgSeq': 871421433}
2025-08-03 00:06:58 | INFO | 收到图片消息: 消息ID:682320157 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 XML:<?xml version="1.0"?><msg><img aeskey="3f98377cf61c963da775ca3bdd469983" encryver="1" cdnthumbaeskey="3f98377cf61c963da775ca3bdd469983" cdnthumburl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" cdnthumblength="3932" cdnthumbheight="592" cdnthumbwidth="269" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" length="669824" cdnbigimgurl="3057020100044b30490201000204c7128a1c02032df6cd0204a19865b40204688e37a3042432396333393565622d623536312d346163642d623330372d353530343334636234393236020405252a010201000405004c53d900" hdlength="565819" md5="9e490dcf99bfdb32a82a13e328baf22e" hevc_mid_size="85052" originsourcemd5="c72796111118bd9fabc2bb43924e03af"><secHashInfoBase64>eyJwaGFzaCI6IjEwMDAxMDEwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNmYwZTdmYjA1NTU2NmE5OWQ1NWE3YWVjNDU2OTJhZjQwNDRiZTJmZGFjYWI5NDRlMjgyNTA0NTJmYWI1MTU1YSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-03 00:06:59 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-03 00:06:59 | INFO | [TimerTask] 缓存图片消息: 682320157
2025-08-03 00:09:05 | DEBUG | 收到消息: {'MsgId': 2126829735, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="0a44697c92145e30c752df695b18653c" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumbaeskey="0a44697c92145e30c752df695b18653c" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" length="4752803" playlength="26" cdnthumblength="4900" cdnthumbwidth="1616" cdnthumbheight="736" fromusername="wxid_zbh5p28da1si22" md5="735230bd8bc84d5110890d60a1711af8" newmd5="0e3e41998358a3bc5bb20b8acc44df6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="735230bd8bc84d5110890d60a1711af8" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754150946, 'MsgSource': '<msgsource>\n\t<videopreloadlen>938413</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>36950b69be3370bb418e52bad9aca0f6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_2/NY0JTl|v1_0vzIgUHp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2808179893355979317, 'MsgSeq': 871421434}
2025-08-03 00:09:05 | INFO | 收到视频消息: 消息ID:2126829735 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="0a44697c92145e30c752df695b18653c" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumbaeskey="0a44697c92145e30c752df695b18653c" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" length="4752803" playlength="26" cdnthumblength="4900" cdnthumbwidth="1616" cdnthumbheight="736" fromusername="wxid_zbh5p28da1si22" md5="735230bd8bc84d5110890d60a1711af8" newmd5="0e3e41998358a3bc5bb20b8acc44df6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="735230bd8bc84d5110890d60a1711af8" isad="0" />
</msg>

2025-08-03 00:09:08 | DEBUG | 收到消息: {'MsgId': 1087376847, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n笑死掉这个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754150950, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VxPycHFX|v1_IeJ5QUI1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 598744952402495699, 'MsgSeq': 871421435}
2025-08-03 00:09:08 | INFO | 收到文本消息: 消息ID:1087376847 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:笑死掉这个
2025-08-03 00:09:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '笑死掉这个' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:09:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['笑死掉这个']
2025-08-03 00:09:08 | DEBUG | 处理消息内容: '笑死掉这个'
2025-08-03 00:09:08 | DEBUG | 消息内容 '笑死掉这个' 不匹配任何命令，忽略
2025-08-03 00:13:52 | DEBUG | 收到消息: {'MsgId': 416051, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754151234, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_IfNohEPL|v1_kJtyuaIR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3024097124520318964, 'MsgSeq': 871421436}
2025-08-03 00:13:52 | INFO | 收到表情消息: 消息ID:416051 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:[破涕为笑]
2025-08-03 00:13:52 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3024097124520318964
2025-08-03 00:14:23 | DEBUG | 收到消息: {'MsgId': 1455097795, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n速度太慢了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754151264, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_hiKsHosB|v1_g7TsNYEr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 16303137411699897, 'MsgSeq': 871421437}
2025-08-03 00:14:23 | INFO | 收到文本消息: 消息ID:1455097795 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:速度太慢了
2025-08-03 00:14:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '速度太慢了' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-03 00:14:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['速度太慢了']
2025-08-03 00:14:23 | DEBUG | 处理消息内容: '速度太慢了'
2025-08-03 00:14:23 | DEBUG | 消息内容 '速度太慢了' 不匹配任何命令，忽略
2025-08-03 00:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 00:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 00:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 00:18:19 | DEBUG | 收到消息: {'MsgId': 1258176295, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>刘能啊</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>2808179893355979317</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_zbh5p28da1si22</chatusr>\n\t\t\t<displayname>赵如初</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;videomsg length="4752803" playlength="26" offset="0" rawoffset="0" fromusername="wxid_zbh5p28da1si22" status="6" cameratype="0" source="1"                                              aeskey="0a44697c92145e30c752df695b18653c" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumblength="4900" cdnthumbwidth="1616" cdnthumbheight="736" cdnthumbaeskey="0a44697c92145e30c752df695b18653c" encryver="1" fileparam="" md5 ="735230bd8bc84d5110890d60a1711af8" newmd5 ="0e3e41998358a3bc5bb20b8acc44df6b" originsourcemd5 ="735230bd8bc84d5110890d60a1711af8"  filekey="***********@chatroom_16369_1754150946" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  &gt;&lt;/videomsg&gt;&lt;statextstr&gt;&lt;/statextstr&gt;&lt;encodejson&gt;&lt;![CDATA[]]&gt;&lt;/encodejson&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;766675134&lt;/sequence_id&gt;\n\t&lt;videopreloadlen&gt;938413&lt;/videopreloadlen&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;36950b69be3370bb418e52bad9aca0f6_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;videomsg_pd cdnvideourl_size="4752803" cdnvideourl_score_all="" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_11N4MZdr|v1_bzFBjUOf&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754150946</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754151501, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3633156ab0c7bc1dc902d58f6f782a93_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_cekj3bTw|v1_AVxqmFr6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1395467639128622046, 'MsgSeq': 871421438}
2025-08-03 00:18:19 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-08-03 00:18:19 | DEBUG | 使用已解析的XML处理引用消息
2025-08-03 00:18:19 | INFO | 收到引用消息: 消息ID:1258176295 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 内容:刘能啊 引用类型:43
2025-08-03 00:18:19 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-03 00:18:19 | INFO | [DouBaoImageToImage] 消息内容: '刘能啊' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-03 00:18:19 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['刘能啊']
2025-08-03 00:18:19 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-03 00:18:19 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-03 00:18:19 | INFO |   - 消息内容: 刘能啊
2025-08-03 00:18:19 | INFO |   - 群组ID: ***********@chatroom
2025-08-03 00:18:19 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-08-03 00:18:19 | INFO |   - 引用信息: {'MsgType': 43, 'Content': '<msg><videomsg length="4752803" playlength="26" offset="0" rawoffset="0" fromusername="wxid_zbh5p28da1si22" status="6" cameratype="0" source="1"                                              aeskey="0a44697c92145e30c752df695b18653c" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumblength="4900" cdnthumbwidth="1616" cdnthumbheight="736" cdnthumbaeskey="0a44697c92145e30c752df695b18653c" encryver="1" fileparam="" md5 ="735230bd8bc84d5110890d60a1711af8" newmd5 ="0e3e41998358a3bc5bb20b8acc44df6b" originsourcemd5 ="735230bd8bc84d5110890d60a1711af8"  filekey="***********@chatroom_16369_1754150946" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>', 'Msgid': '2808179893355979317', 'NewMsgId': '2808179893355979317', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '赵如初', 'MsgSource': '<msgsource><sequence_id>766675134</sequence_id>\n\t<videopreloadlen>938413</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>36950b69be3370bb418e52bad9aca0f6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<videomsg_pd cdnvideourl_size="4752803" cdnvideourl_score_all="" />\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_11N4MZdr|v1_bzFBjUOf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754150946', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-08-03 00:18:19 | INFO |   - 引用消息ID: 
2025-08-03 00:18:19 | INFO |   - 引用消息类型: 
2025-08-03 00:18:19 | INFO |   - 引用消息内容: <msg><videomsg length="4752803" playlength="26" offset="0" rawoffset="0" fromusername="wxid_zbh5p28da1si22" status="6" cameratype="0" source="1"                                              aeskey="0a44697c92145e30c752df695b18653c" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204116832700204688e3821042462633334623032622d373264652d343439302d383764612d6430356133393036656162380204052408040201000405004c53db00" cdnthumblength="4900" cdnthumbwidth="1616" cdnthumbheight="736" cdnthumbaeskey="0a44697c92145e30c752df695b18653c" encryver="1" fileparam="" md5 ="735230bd8bc84d5110890d60a1711af8" newmd5 ="0e3e41998358a3bc5bb20b8acc44df6b" originsourcemd5 ="735230bd8bc84d5110890d60a1711af8"  filekey="***********@chatroom_16369_1754150946" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>
2025-08-03 00:18:19 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-08-03 00:20:07 | DEBUG | 收到消息: {'MsgId': 273555955, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n点歌 Nocturnal 歌手adam sellouk'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754151608, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>25</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_4/F3Bv/B|v1_E3j+q5HF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙 : 点歌 Nocturnal 歌手adam sellouk', 'NewMsgId': 3625077225726899026, 'MsgSeq': 871421439}
2025-08-03 00:20:07 | INFO | 收到文本消息: 消息ID:273555955 来自:55878994168@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:点歌 Nocturnal 歌手adam sellouk
2025-08-03 00:20:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '点歌 Nocturnal 歌手adam sellouk' from wxid_rwfb9vuy93jn22 in 55878994168@chatroom
2025-08-03 00:20:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['点歌', 'Nocturnal', '歌手adam sellouk']
2025-08-03 00:20:10 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>None</title><des>None</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>None?src=无损音质 FLAC_ Bytes?src=128_?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>None?src=无损音质 FLAC_ Bytes?src=128_?from=longzhu_api</lowdataurl><recorditem/><thumburl>None</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric></songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>None</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-08-03 00:20:10 | DEBUG | 处理消息内容: '点歌 Nocturnal 歌手adam sellouk'
2025-08-03 00:20:10 | DEBUG | 消息内容 '点歌 Nocturnal 歌手adam sellouk' 不匹配任何命令，忽略
2025-08-03 00:28:02 | DEBUG | 收到消息: {'MsgId': 183305660, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n哎 真挺像刘能'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754152083, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_027YcNI9|v1_3HyLtPrh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4156505053879462720, 'MsgSeq': 871421442}
2025-08-03 00:28:02 | INFO | 收到文本消息: 消息ID:183305660 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:哎 真挺像刘能
2025-08-03 00:28:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哎 真挺像刘能' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:28:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['哎', '真挺像刘能']
2025-08-03 00:28:02 | DEBUG | 处理消息内容: '哎 真挺像刘能'
2025-08-03 00:28:02 | DEBUG | 消息内容 '哎 真挺像刘能' 不匹配任何命令，忽略
2025-08-03 00:28:04 | DEBUG | 收到消息: {'MsgId': 2080208280, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n哈哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754152085, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_g1O72OCh|v1_gbta6LOF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8619066386452175989, 'MsgSeq': 871421443}
2025-08-03 00:28:04 | INFO | 收到文本消息: 消息ID:2080208280 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:哈哈哈哈哈哈哈
2025-08-03 00:28:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈哈哈' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:28:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈哈哈']
2025-08-03 00:28:04 | DEBUG | 处理消息内容: '哈哈哈哈哈哈哈'
2025-08-03 00:28:04 | DEBUG | 消息内容 '哈哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-03 00:29:57 | DEBUG | 收到消息: {'MsgId': 453527906, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_j2kp7us99uv222:\n这么逗吗这个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754152198, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_38kWYg1B|v1_kTB6GU3Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7732378561102860850, 'MsgSeq': 871421444}
2025-08-03 00:29:57 | INFO | 收到文本消息: 消息ID:453527906 来自:***********@chatroom 发送人:wxid_j2kp7us99uv222 @:[] 内容:这么逗吗这个
2025-08-03 00:29:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这么逗吗这个' from wxid_j2kp7us99uv222 in ***********@chatroom
2025-08-03 00:29:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['这么逗吗这个']
2025-08-03 00:29:57 | DEBUG | 处理消息内容: '这么逗吗这个'
2025-08-03 00:29:57 | DEBUG | 消息内容 '这么逗吗这个' 不匹配任何命令，忽略
2025-08-03 00:44:33 | DEBUG | 收到消息: {'MsgId': 1395474904, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>1395467639128622046</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_snv13qf05qjx11</chatusr>\n\t\t\t<displayname>夕未语</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg appid=""  sdkver="0"&gt;&lt;title&gt;刘能啊&lt;/title&gt;&lt;type&gt;57&lt;/type&gt;&lt;appattach&gt;&lt;cdnthumbaeskey&gt;&lt;/cdnthumbaeskey&gt;&lt;aeskey&gt;&lt;/aeskey&gt;&lt;/appattach&gt;&lt;/appmsg&gt;&lt;fromusername&gt;wxid_snv13qf05qjx11&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;appname&gt;&lt;/appname&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;831133795&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;3633156ab0c7bc1dc902d58f6f782a93_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_d1jsgcRH|v1_bL+vm0jk&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754151501</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_1ul5r40nibpn12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153074, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>5e1e9595d2c71a4685d76989d5cb7a48_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_kHAhJM73|v1_SB0J/lbm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2958393299820146523, 'MsgSeq': 871421445}
2025-08-03 00:44:33 | DEBUG | 从群聊消息中提取发送者: wxid_1ul5r40nibpn12
2025-08-03 00:44:33 | DEBUG | 使用已解析的XML处理引用消息
2025-08-03 00:44:33 | INFO | 收到引用消息: 消息ID:1395474904 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 内容:哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈 引用类型:49
2025-08-03 00:44:33 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-03 00:44:33 | INFO | [DouBaoImageToImage] 消息内容: '哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-03 00:44:33 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈']
2025-08-03 00:44:33 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-03 00:44:33 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-03 00:44:33 | INFO |   - 消息内容: 哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈
2025-08-03 00:44:33 | INFO |   - 群组ID: ***********@chatroom
2025-08-03 00:44:33 | INFO |   - 发送人: wxid_1ul5r40nibpn12
2025-08-03 00:44:33 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg appid=""  sdkver="0"><title>刘能啊</title><type>57</type><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach></appmsg><fromusername>wxid_snv13qf05qjx11</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>', 'Msgid': '1395467639128622046', 'NewMsgId': '1395467639128622046', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '夕未语', 'MsgSource': '<msgsource><sequence_id>831133795</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3633156ab0c7bc1dc902d58f6f782a93_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_d1jsgcRH|v1_bL+vm0jk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754151501', 'SenderWxid': 'wxid_1ul5r40nibpn12'}
2025-08-03 00:44:33 | INFO |   - 引用消息ID: 
2025-08-03 00:44:33 | INFO |   - 引用消息类型: 
2025-08-03 00:44:33 | INFO |   - 引用消息内容: <msg><appmsg appid=""  sdkver="0"><title>刘能啊</title><type>57</type><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach></appmsg><fromusername>wxid_snv13qf05qjx11</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>
2025-08-03 00:44:33 | INFO |   - 引用消息发送人: wxid_1ul5r40nibpn12
2025-08-03 00:44:51 | DEBUG | 收到消息: {'MsgId': 670404235, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="86da76f1dca87d9054c14953214a281a" len="846049" productid="" androidmd5="86da76f1dca87d9054c14953214a281a" androidlen="846049" s60v3md5="86da76f1dca87d9054c14953214a281a" s60v3len="846049" s60v5md5="86da76f1dca87d9054c14953214a281a" s60v5len="846049" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=86da76f1dca87d9054c14953214a281a&amp;filekey=30440201010430302e02016e0402535a0420383664613736663164636138376439303534633134393533323134613238316102030ce8e1040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265b285e2000267a9626c08880000006e01004fb1535a05869bc1e65c7f9dd&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=1590743d81848f6bc03cd3f54d0ab75e&amp;filekey=30440201010430302e02016e0402535a0420313539303734336438313834386636626330336364336635346430616237356502030ce8f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265b285e20004a40f626c08880000006e02004fb2535a05869bc1e65c7f9e8&amp;ef=2&amp;bizid=1022" aeskey="1776246eb4474a99b1767f67bc7dc720" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=d1c05eda4bb4b27d47c879b2ed84a6a6&amp;filekey=30440201010430302e02016e0402535a042064316330356564613462623462323764343763383739623265643834613661360203019a20040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265b285e20006204e626c08880000006e03004fb3535a05869bc1e65c7f9f5&amp;ef=3&amp;bizid=1022" externmd5="bbb79513621dbaffaa412ecb6ef3eb3d" width="282" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153092, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Qoy9dYBe|v1_z/p39lbh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1826672433904950965, 'MsgSeq': 871421446}
2025-08-03 00:44:51 | INFO | 收到表情消息: 消息ID:670404235 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:86da76f1dca87d9054c14953214a281a 大小:846049
2025-08-03 00:44:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1826672433904950965
2025-08-03 00:44:56 | DEBUG | 收到消息: {'MsgId': 655902073, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n诶我艹这把我笑的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153097, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qgyAMLqY|v1_PJN0K9q6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3157226709089206927, 'MsgSeq': 871421447}
2025-08-03 00:44:56 | INFO | 收到文本消息: 消息ID:655902073 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:诶我艹这把我笑的
2025-08-03 00:44:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '诶我艹这把我笑的' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-03 00:44:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['诶我艹这把我笑的']
2025-08-03 00:44:56 | DEBUG | 处理消息内容: '诶我艹这把我笑的'
2025-08-03 00:44:56 | DEBUG | 消息内容 '诶我艹这把我笑的' 不匹配任何命令，忽略
2025-08-03 00:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 00:46:01 | DEBUG | 收到消息: {'MsgId': 106717639, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n像是被狗追着跑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153163, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Q6srf3QV|v1_0ASWhGMx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7946824151098041067, 'MsgSeq': 871421448}
2025-08-03 00:46:01 | INFO | 收到文本消息: 消息ID:106717639 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:像是被狗追着跑
2025-08-03 00:46:01 | DEBUG | [DouBaoImageToImage] 收到文本消息: '像是被狗追着跑' from wxid_4183511832012 in ***********@chatroom
2025-08-03 00:46:01 | DEBUG | [DouBaoImageToImage] 命令解析: ['像是被狗追着跑']
2025-08-03 00:46:01 | DEBUG | 处理消息内容: '像是被狗追着跑'
2025-08-03 00:46:01 | DEBUG | 消息内容 '像是被狗追着跑' 不匹配任何命令，忽略
2025-08-03 00:46:17 | DEBUG | 收到消息: {'MsgId': 894373555, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153178, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ttoDf+Pg|v1_+EVjWy22</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2760889239946150352, 'MsgSeq': 871421449}
2025-08-03 00:46:17 | INFO | 收到文本消息: 消息ID:894373555 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:哈哈
2025-08-03 00:46:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈' from wxid_4183511832012 in ***********@chatroom
2025-08-03 00:46:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈']
2025-08-03 00:46:17 | DEBUG | 处理消息内容: '哈哈'
2025-08-03 00:46:17 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-08-03 00:46:50 | DEBUG | 收到消息: {'MsgId': 421799523, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="85dfb9028e77eb52658c26e941be26b5" len = "111202" productid="" androidmd5="85dfb9028e77eb52658c26e941be26b5" androidlen="111202" s60v3md5 = "85dfb9028e77eb52658c26e941be26b5" s60v3len="111202" s60v5md5 = "85dfb9028e77eb52658c26e941be26b5" s60v5len="111202" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=85dfb9028e77eb52658c26e941be26b5&amp;filekey=30440201010430302e02016e0402534804203835646662393032386537376562353236353863323665393431626532366235020301b262040d00000004627466730000000132&amp;hy=SH&amp;storeid=267050e0b0001c125ae8b37fe0000006e01004fb153482137c0d15728f3ca1&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=5c6ade52c8e107d16e6acfff111e2851&amp;filekey=30440201010430302e02016e0402534804203563366164653532633865313037643136653661636666663131316532383531020301b270040d00000004627466730000000132&amp;hy=SH&amp;storeid=267050e0b000287c8ae8b37fe0000006e02004fb253482137c0d15728f3cc0&amp;ef=2&amp;bizid=1022" aeskey= "3d063573dfa049e28739d97b85d1a2ba" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=b96c317dd71ecc56dfaa07ede4400f92&amp;filekey=3043020101042f302d02016e0402534804206239366333313764643731656363353664666161303765646534343030663932020217d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267050e0b00039dcaae8b37fe0000006e03004fb353482137c0d15728f3ce2&amp;ef=3&amp;bizid=1022" externmd5 = "e7d57958a574dfb3cae34c8f92bb39e7" width= "300" height= "283" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153211, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0ioq7sfX|v1_dI9reeD7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8262608948404030956, 'MsgSeq': 871421450}
2025-08-03 00:46:50 | INFO | 收到表情消息: 消息ID:421799523 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:85dfb9028e77eb52658c26e941be26b5 大小:111202
2025-08-03 00:46:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8262608948404030956
2025-08-03 00:49:25 | DEBUG | 收到消息: {'MsgId': 899229185, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@小艺\u2005对，就是'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153367, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_4183511832012]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VrPZ/lUX|v1_UIlHgvY/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8424279191085911702, 'MsgSeq': 871421451}
2025-08-03 00:49:25 | INFO | 收到文本消息: 消息ID:899229185 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_4183511832012'] 内容:@小艺 对，就是
2025-08-03 00:49:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@小艺 对，就是' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:49:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['@小艺\u2005对，就是']
2025-08-03 00:49:25 | DEBUG | 处理消息内容: '@小艺 对，就是'
2025-08-03 00:49:25 | DEBUG | 消息内容 '@小艺 对，就是' 不匹配任何命令，忽略
2025-08-03 00:50:26 | DEBUG | 收到消息: {'MsgId': 1813756140, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n有没有互送礼物的。舞团排行榜任务'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153427, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Jd0vtJXg|v1_Ib5BcHul</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5907160974099635182, 'MsgSeq': 871421452}
2025-08-03 00:50:26 | INFO | 收到文本消息: 消息ID:1813756140 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:有没有互送礼物的。舞团排行榜任务
2025-08-03 00:50:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有没有互送礼物的。舞团排行榜任务' from wxid_4183511832012 in ***********@chatroom
2025-08-03 00:50:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['有没有互送礼物的。舞团排行榜任务']
2025-08-03 00:50:26 | DEBUG | 处理消息内容: '有没有互送礼物的。舞团排行榜任务'
2025-08-03 00:50:26 | DEBUG | 消息内容 '有没有互送礼物的。舞团排行榜任务' 不匹配任何命令，忽略
2025-08-03 00:50:50 | DEBUG | 收到消息: {'MsgId': 1899375263, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="9b53281562d85f768b1d0835645ae432" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204706632700204688e41eb042437343766663661322d666162612d343363622d396538332d3163323062353436613138650204052408040201000405004c511f00" cdnthumbaeskey="9b53281562d85f768b1d0835645ae432" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204706632700204688e41eb042437343766663661322d666162612d343363622d396538332d3163323062353436613138650204052408040201000405004c511f00" length="1586550" playlength="14" cdnthumblength="31973" cdnthumbwidth="620" cdnthumbheight="277" fromusername="wxid_zbh5p28da1si22" md5="096742f34d0ade685ff4ce07b58cac38" newmd5="890fde5a1f119460df989ff2fe761ccb" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="f2637847b03ff966ce2e092c70746ae7" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153452, 'MsgSource': '<msgsource>\n\t<videopreloadlen>581030</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>f428acb437032edf0da9511e5fb7bc32_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_z99+Zm5t|v1_O1reDUyF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5699015445189087559, 'MsgSeq': 871421453}
2025-08-03 00:50:50 | INFO | 收到视频消息: 消息ID:1899375263 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="9b53281562d85f768b1d0835645ae432" cdnvideourl="3057020100044b30490201000204acc2e5ef02032f58b70204706632700204688e41eb042437343766663661322d666162612d343363622d396538332d3163323062353436613138650204052408040201000405004c511f00" cdnthumbaeskey="9b53281562d85f768b1d0835645ae432" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b70204706632700204688e41eb042437343766663661322d666162612d343363622d396538332d3163323062353436613138650204052408040201000405004c511f00" length="1586550" playlength="14" cdnthumblength="31973" cdnthumbwidth="620" cdnthumbheight="277" fromusername="wxid_zbh5p28da1si22" md5="096742f34d0ade685ff4ce07b58cac38" newmd5="890fde5a1f119460df989ff2fe761ccb" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="f2637847b03ff966ce2e092c70746ae7" isad="0" />
</msg>

2025-08-03 00:50:54 | DEBUG | 收到消息: {'MsgId': 1218678537, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n看，一只的，两只的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153456, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_rOseDEuB|v1_A4GUBEeU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4527861643927303677, 'MsgSeq': 871421454}
2025-08-03 00:50:54 | INFO | 收到文本消息: 消息ID:1218678537 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:看，一只的，两只的
2025-08-03 00:50:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '看，一只的，两只的' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:50:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['看，一只的，两只的']
2025-08-03 00:50:54 | DEBUG | 处理消息内容: '看，一只的，两只的'
2025-08-03 00:50:54 | DEBUG | 消息内容 '看，一只的，两只的' 不匹配任何命令，忽略
2025-08-03 00:51:10 | DEBUG | 收到消息: {'MsgId': 555471284, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n一只的是牵着狗带人跑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153472, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_dAdVHEoW|v1_YwK2IARb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3176794709434221495, 'MsgSeq': 871421455}
2025-08-03 00:51:10 | INFO | 收到文本消息: 消息ID:555471284 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:一只的是牵着狗带人跑
2025-08-03 00:51:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '一只的是牵着狗带人跑' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:51:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['一只的是牵着狗带人跑']
2025-08-03 00:51:10 | DEBUG | 处理消息内容: '一只的是牵着狗带人跑'
2025-08-03 00:51:10 | DEBUG | 消息内容 '一只的是牵着狗带人跑' 不匹配任何命令，忽略
2025-08-03 00:52:07 | DEBUG | 收到消息: {'MsgId': 1108406287, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153528, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pIdJWoKm|v1_9BTyFqus</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5255918493678559999, 'MsgSeq': 871421456}
2025-08-03 00:52:07 | INFO | 收到文本消息: 消息ID:1108406287 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:哈哈哈哈哈
2025-08-03 00:52:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈' from wxid_4183511832012 in ***********@chatroom
2025-08-03 00:52:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈']
2025-08-03 00:52:07 | DEBUG | 处理消息内容: '哈哈哈哈哈'
2025-08-03 00:52:07 | DEBUG | 消息内容 '哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-03 00:53:18 | DEBUG | 收到消息: {'MsgId': 1382258208, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n不知道三阶的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153600, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gs6Ai0Xg|v1_x8PTJ786</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4357121388809283721, 'MsgSeq': 871421457}
2025-08-03 00:53:18 | INFO | 收到文本消息: 消息ID:1382258208 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:不知道三阶的
2025-08-03 00:53:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不知道三阶的' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:53:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['不知道三阶的']
2025-08-03 00:53:18 | DEBUG | 处理消息内容: '不知道三阶的'
2025-08-03 00:53:18 | DEBUG | 消息内容 '不知道三阶的' 不匹配任何命令，忽略
2025-08-03 00:55:43 | DEBUG | 收到消息: {'MsgId': 514813611, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="8d0c79891f6f53c03274e49d17675679" cdnvideourl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" cdnthumbaeskey="8d0c79891f6f53c03274e49d17675679" cdnthumburl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" length="1215217" playlength="13" cdnthumblength="5080" cdnthumbwidth="224" cdnthumbheight="398" fromusername="wxid_ugv5ryus4gz622" md5="d225c2215df47618b07cbcbe86d985d1" newmd5="0fb65d11c33f7c4d83665e1c82bf1e6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153745, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>647667a039b884246d0888fed02c77ac_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VxSRHNpd|v1_RXD6sR8V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9050362664030056527, 'MsgSeq': 871421458}
2025-08-03 00:55:43 | INFO | 收到视频消息: 消息ID:514813611 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="8d0c79891f6f53c03274e49d17675679" cdnvideourl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" cdnthumbaeskey="8d0c79891f6f53c03274e49d17675679" cdnthumburl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" length="1215217" playlength="13" cdnthumblength="5080" cdnthumbwidth="224" cdnthumbheight="398" fromusername="wxid_ugv5ryus4gz622" md5="d225c2215df47618b07cbcbe86d985d1" newmd5="0fb65d11c33f7c4d83665e1c82bf1e6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-08-03 00:55:46 | DEBUG | 收到消息: {'MsgId': 131177219, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n三阶的这样儿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153748, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0L/elFmd|v1_VcgVpg6s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6460881253571789953, 'MsgSeq': 871421459}
2025-08-03 00:55:46 | INFO | 收到文本消息: 消息ID:131177219 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:三阶的这样儿
2025-08-03 00:55:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '三阶的这样儿' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:55:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['三阶的这样儿']
2025-08-03 00:55:46 | DEBUG | 处理消息内容: '三阶的这样儿'
2025-08-03 00:55:46 | DEBUG | 消息内容 '三阶的这样儿' 不匹配任何命令，忽略
2025-08-03 00:55:51 | DEBUG | 收到消息: {'MsgId': 13681600, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n没一个正常的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153753, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Pvs9ItfK|v1_VZSVh8fS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3833911589593910003, 'MsgSeq': 871421460}
2025-08-03 00:55:51 | INFO | 收到文本消息: 消息ID:13681600 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:没一个正常的
2025-08-03 00:55:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没一个正常的' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:55:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['没一个正常的']
2025-08-03 00:55:51 | DEBUG | 处理消息内容: '没一个正常的'
2025-08-03 00:55:51 | DEBUG | 消息内容 '没一个正常的' 不匹配任何命令，忽略
2025-08-03 00:56:14 | DEBUG | 收到消息: {'MsgId': 1322244450, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n哈哈哈哈哈哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153776, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_kObXUfaA|v1_xsuvpwkD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7830181773411336831, 'MsgSeq': 871421461}
2025-08-03 00:56:14 | INFO | 收到文本消息: 消息ID:1322244450 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:哈哈哈哈哈哈哈哈哈哈哈
2025-08-03 00:56:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈哈哈哈哈哈哈' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-03 00:56:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈哈哈哈哈哈哈']
2025-08-03 00:56:14 | DEBUG | 处理消息内容: '哈哈哈哈哈哈哈哈哈哈哈'
2025-08-03 00:56:14 | DEBUG | 消息内容 '哈哈哈哈哈哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-03 00:56:59 | DEBUG | 收到消息: {'MsgId': 247618227, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n溜冰呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153821, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_1/AwHXH/|v1_r/MrMWP+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1473293558186596838, 'MsgSeq': 871421462}
2025-08-03 00:56:59 | INFO | 收到文本消息: 消息ID:247618227 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:溜冰呢
2025-08-03 00:56:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '溜冰呢' from wxid_snv13qf05qjx11 in ***********@chatroom
2025-08-03 00:56:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['溜冰呢']
2025-08-03 00:56:59 | DEBUG | 处理消息内容: '溜冰呢'
2025-08-03 00:56:59 | DEBUG | 消息内容 '溜冰呢' 不匹配任何命令，忽略
2025-08-03 00:57:07 | DEBUG | 收到消息: {'MsgId': 2034429965, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n服了 真滴'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153829, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_g2vYLAvD|v1_Izj9I0yN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3690126054102820869, 'MsgSeq': 871421463}
2025-08-03 00:57:07 | INFO | 收到文本消息: 消息ID:2034429965 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:服了 真滴
2025-08-03 00:57:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '服了 真滴' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:57:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['服了', '真滴']
2025-08-03 00:57:07 | DEBUG | 处理消息内容: '服了 真滴'
2025-08-03 00:57:07 | DEBUG | 消息内容 '服了 真滴' 不匹配任何命令，忽略
2025-08-03 00:57:10 | DEBUG | 收到消息: {'MsgId': 275015064, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n这拖的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153831, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_fNcpBXcS|v1_qtI41s4l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4241173877586116553, 'MsgSeq': 871421464}
2025-08-03 00:57:10 | INFO | 收到文本消息: 消息ID:275015064 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:这拖的
2025-08-03 00:57:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这拖的' from wxid_zbh5p28da1si22 in ***********@chatroom
2025-08-03 00:57:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['这拖的']
2025-08-03 00:57:10 | DEBUG | 处理消息内容: '这拖的'
2025-08-03 00:57:10 | DEBUG | 消息内容 '这拖的' 不匹配任何命令，忽略
2025-08-03 00:57:14 | DEBUG | 收到消息: {'MsgId': 424694668, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n[偷笑][偷笑][偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153835, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wJioDmdc|v1_4tlYgyFv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5457749960639358827, 'MsgSeq': 871421465}
2025-08-03 00:57:14 | INFO | 收到表情消息: 消息ID:424694668 来自:***********@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:[偷笑][偷笑][偷笑]
2025-08-03 00:57:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5457749960639358827
2025-08-03 00:57:25 | DEBUG | 收到消息: {'MsgId': 2015290740, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>噗哈哈哈 这个更搞笑</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>9050362664030056527</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ugv5ryus4gz622</chatusr>\n\t\t\t<displayname>悦菟ིྀ</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;647667a039b884246d0888fed02c77ac_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;videomsg_pd cdnvideourl_size="1215217" cdnvideourl_score_all="15:10000;" cdnvideourl_pd_pri="30" cdnvideourl_pd="0" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_MxATjiAu|v1_0M87Wc3/&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754153745</createtime>\n\t\t\t<content>wxid_ugv5ryus4gz622:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;videomsg aeskey="8d0c79891f6f53c03274e49d17675679" cdnvideourl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" cdnthumbaeskey="8d0c79891f6f53c03274e49d17675679" cdnthumburl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" length="1215217" playlength="13" cdnthumblength="5080" cdnthumbwidth="224" cdnthumbheight="398" fromusername="wxid_ugv5ryus4gz622" md5="d225c2215df47618b07cbcbe86d985d1" newmd5="0fb65d11c33f7c4d83665e1c82bf1e6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" /&gt;\n&lt;/msg&gt;\n</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153846, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>a393c759c4ef9c0548893d2de3dd39f9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_30NtApVE|v1_ZGVFfaLN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3196876167715381131, 'MsgSeq': 871421466}
2025-08-03 00:57:25 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-08-03 00:57:25 | DEBUG | 使用已解析的XML处理引用消息
2025-08-03 00:57:25 | INFO | 收到引用消息: 消息ID:2015290740 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 内容:噗哈哈哈 这个更搞笑 引用类型:43
2025-08-03 00:57:25 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-03 00:57:25 | INFO | [DouBaoImageToImage] 消息内容: '噗哈哈哈 这个更搞笑' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-03 00:57:25 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['噗哈哈哈', '这个更搞笑']
2025-08-03 00:57:25 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-03 00:57:25 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-03 00:57:25 | INFO |   - 消息内容: 噗哈哈哈 这个更搞笑
2025-08-03 00:57:25 | INFO |   - 群组ID: ***********@chatroom
2025-08-03 00:57:25 | INFO |   - 发送人: wxid_ikxxrwasicud11
2025-08-03 00:57:25 | INFO |   - 引用信息: {'MsgType': 43, 'Content': 'wxid_ugv5ryus4gz622:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="8d0c79891f6f53c03274e49d17675679" cdnvideourl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" cdnthumbaeskey="8d0c79891f6f53c03274e49d17675679" cdnthumburl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" length="1215217" playlength="13" cdnthumblength="5080" cdnthumbwidth="224" cdnthumbheight="398" fromusername="wxid_ugv5ryus4gz622" md5="d225c2215df47618b07cbcbe86d985d1" newmd5="0fb65d11c33f7c4d83665e1c82bf1e6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n', 'Msgid': '9050362664030056527', 'NewMsgId': '9050362664030056527', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '悦菟ིྀ', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>647667a039b884246d0888fed02c77ac_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<videomsg_pd cdnvideourl_size="1215217" cdnvideourl_score_all="15:10000;" cdnvideourl_pd_pri="30" cdnvideourl_pd="0" />\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_MxATjiAu|v1_0M87Wc3/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754153745', 'SenderWxid': 'wxid_ikxxrwasicud11'}
2025-08-03 00:57:25 | INFO |   - 引用消息ID: 
2025-08-03 00:57:25 | INFO |   - 引用消息类型: 
2025-08-03 00:57:25 | INFO |   - 引用消息内容: wxid_ugv5ryus4gz622:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="8d0c79891f6f53c03274e49d17675679" cdnvideourl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" cdnthumbaeskey="8d0c79891f6f53c03274e49d17675679" cdnthumburl="3057020100044b30490201000204524af8fe02032dc78902047578ac7b0204688e430e042437663765616463392d666337372d343933612d396130302d32313262643238613335653902040d2808040201000405004c537500" length="1215217" playlength="13" cdnthumblength="5080" cdnthumbwidth="224" cdnthumbheight="398" fromusername="wxid_ugv5ryus4gz622" md5="d225c2215df47618b07cbcbe86d985d1" newmd5="0fb65d11c33f7c4d83665e1c82bf1e6b" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-08-03 00:57:25 | INFO |   - 引用消息发送人: wxid_ikxxrwasicud11
2025-08-03 00:57:31 | DEBUG | 收到消息: {'MsgId': 337801162, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n鞋底儿都得磨漏咯'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153853, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_dhAA1ccO|v1_6pty4tVH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2231290584938898192, 'MsgSeq': 871421467}
2025-08-03 00:57:31 | INFO | 收到文本消息: 消息ID:337801162 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:鞋底儿都得磨漏咯
2025-08-03 00:57:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '鞋底儿都得磨漏咯' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:57:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['鞋底儿都得磨漏咯']
2025-08-03 00:57:31 | DEBUG | 处理消息内容: '鞋底儿都得磨漏咯'
2025-08-03 00:57:31 | DEBUG | 消息内容 '鞋底儿都得磨漏咯' 不匹配任何命令，忽略
2025-08-03 00:58:55 | DEBUG | 收到消息: {'MsgId': 558697081, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n哈哈哈哈哈哈哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153936, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ymgQvfe0|v1_Ose2lSgb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3166447282074133038, 'MsgSeq': 871421468}
2025-08-03 00:58:55 | INFO | 收到文本消息: 消息ID:558697081 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:哈哈哈哈哈哈哈哈哈哈哈哈
2025-08-03 00:58:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈哈哈哈哈哈哈哈' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-03 00:58:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈哈哈哈哈哈哈哈']
2025-08-03 00:58:55 | DEBUG | 处理消息内容: '哈哈哈哈哈哈哈哈哈哈哈哈'
2025-08-03 00:58:55 | DEBUG | 消息内容 '哈哈哈哈哈哈哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-03 00:58:57 | DEBUG | 收到消息: {'MsgId': 470251782, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n我了个豆啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153938, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qomzRFpo|v1_X+amEY9E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3278514604206624174, 'MsgSeq': 871421469}
2025-08-03 00:58:57 | INFO | 收到文本消息: 消息ID:470251782 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:我了个豆啊
2025-08-03 00:58:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我了个豆啊' from wxid_1ul5r40nibpn12 in ***********@chatroom
2025-08-03 00:58:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['我了个豆啊']
2025-08-03 00:58:57 | DEBUG | 处理消息内容: '我了个豆啊'
2025-08-03 00:58:57 | DEBUG | 消息内容 '我了个豆啊' 不匹配任何命令，忽略
2025-08-03 00:59:18 | DEBUG | 收到消息: {'MsgId': 1776561995, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n谁曾想 谁曾想'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153960, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_7n0n3HpW|v1_yCMj9vP5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3999040252871684012, 'MsgSeq': 871421470}
2025-08-03 00:59:18 | INFO | 收到文本消息: 消息ID:1776561995 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:谁曾想 谁曾想
2025-08-03 00:59:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '谁曾想 谁曾想' from wxid_ugv5ryus4gz622 in ***********@chatroom
2025-08-03 00:59:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['谁曾想', '谁曾想']
2025-08-03 00:59:18 | DEBUG | 处理消息内容: '谁曾想 谁曾想'
2025-08-03 00:59:18 | DEBUG | 消息内容 '谁曾想 谁曾想' 不匹配任何命令，忽略
2025-08-03 00:59:26 | DEBUG | 收到消息: {'MsgId': 767921206, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n捅了狗窝了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754153967, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VU3e8YYA|v1_1+lZ7Ckd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2269538689685663195, 'MsgSeq': 871421471}
2025-08-03 00:59:26 | INFO | 收到文本消息: 消息ID:767921206 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:捅了狗窝了
2025-08-03 00:59:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '捅了狗窝了' from wxid_ikxxrwasicud11 in ***********@chatroom
2025-08-03 00:59:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['捅了狗窝了']
2025-08-03 00:59:26 | DEBUG | 处理消息内容: '捅了狗窝了'
2025-08-03 00:59:26 | DEBUG | 消息内容 '捅了狗窝了' 不匹配任何命令，忽略
2025-08-03 01:04:22 | DEBUG | 收到消息: {'MsgId': 523382711, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754154263, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_e3tbwRJi|v1_RUxA/WHT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4595929144774167934, 'MsgSeq': 871421472}
2025-08-03 01:04:22 | INFO | 收到文本消息: 消息ID:523382711 来自:***********@chatroom 发送人:wxid_4183511832012 @:[] 内容:签到
2025-08-03 01:04:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '签到' from wxid_4183511832012 in ***********@chatroom
2025-08-03 01:04:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['签到']
2025-08-03 01:04:22 | DEBUG | 处理消息内容: '签到'
2025-08-03 01:04:22 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-03 01:04:22 | INFO | 数据库: 用户wxid_4183511832012登录时间设置为2025-08-03 00:00:00+08:00
2025-08-03 01:04:22 | INFO | 数据库: 用户wxid_4183511832012连续签到天数设置为2
2025-08-03 01:04:22 | INFO | 数据库: 用户wxid_4183511832012积分增加14
2025-08-03 01:04:23 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_4183511832012'] 内容:@🥤 
-----XYBot-----
签到成功！你领到了 14 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 2 天！[爱心]
2025-08-03 01:04:36 | DEBUG | 收到消息: {'MsgId': 1161938365, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_4183511832012:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>唱舞签到</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>http://reserve.fhsj.xipu.com/sign/index.html#/index?wx_open_id=o8rBR58YgBuSBxkL0cE-s-Sl3sc0&amp;wx_open_id_check=4c412f41949430cfa5785a390679714c</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal>http://reserve.fhsj.xipu.com/api/sign/index</shareUrlOriginal>\n\t\t\t<shareUrlOpen>http://reserve.fhsj.xipu.com/sign/index.html#/index?wx_open_id=o8rBR58YgBuSBxkL0cE-s-Sl3sc0&amp;wx_open_id_check=4c412f41949430cfa5785a390679714c</shareUrlOpen>\n\t\t\t<jsAppId />\n\t\t\t<publisherId>custom_menu</publisherId>\n\t\t\t<publisherReqId>1807612612</publisherReqId>\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID />\n\t\t\t<finderUsername />\n\t\t\t<finderObjectID />\n\t\t\t<finderNonceID />\n\t\t\t<liveStatus />\n\t\t\t<appId />\n\t\t\t<pagePath />\n\t\t\t<productId />\n\t\t\t<coverUrl />\n\t\t\t<productTitle />\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg />\n\t\t\t<platformName />\n\t\t\t<shopWindowId />\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource />\n\t\t\t<sellingPriceWording />\n\t\t\t<platformIconURL />\n\t\t\t<firstProductTagURL />\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL />\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording />\n\t\t\t<secondGuaranteeWording />\n\t\t\t<thirdGuaranteeWording />\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID />\n\t\t\t<promoterKey />\n\t\t\t<discountWording />\n\t\t\t<priceSuffixDescription />\n\t\t\t<productCardKey />\n\t\t\t<isWxShop />\n\t\t\t<brandIconUrl />\n\t\t\t<rIconUrl />\n\t\t\t<rIconUrlDarkMode />\n\t\t\t<topShopIconUrl />\n\t\t\t<topShopIconUrlDarkMode />\n\t\t\t<simplifyTopShopIconUrl />\n\t\t\t<simplifyTopShopIconUrlDarkmode />\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID />\n\t\t\t<orderID />\n\t\t\t<path />\n\t\t\t<priceWording />\n\t\t\t<stateWording />\n\t\t\t<productImageURL />\n\t\t\t<products />\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording />\n\t\t\t<newStateWording />\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername />\n\t\t\t<avatar />\n\t\t\t<nickname />\n\t\t\t<commodityInStockCount />\n\t\t\t<appId />\n\t\t\t<path />\n\t\t\t<appUsername />\n\t\t\t<query />\n\t\t\t<liteAppId />\n\t\t\t<liteAppPath />\n\t\t\t<liteAppQuery />\n\t\t\t<platformTagURL />\n\t\t\t<saleWording />\n\t\t\t<lastGMsgID />\n\t\t\t<profileTypeWording />\n\t\t\t<saleWordingExtra />\n\t\t\t<isWxShop />\n\t\t\t<platformIconUrl />\n\t\t\t<brandIconUrl />\n\t\t\t<description />\n\t\t\t<backgroundUrl />\n\t\t\t<darkModePlatformIconUrl />\n\t\t\t<rIconUrl />\n\t\t\t<rIconUrlDarkMode />\n\t\t\t<rWords />\n\t\t\t<topShopIconUrl />\n\t\t\t<topShopIconUrlDarkMode />\n\t\t\t<simplifyTopShopIconUrl />\n\t\t\t<simplifyTopShopIconUrlDarkmode />\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID />\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_4183511832012</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754154278, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>8b7217789ed6c63f03703b96f2d7572b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_R/RJ9wfN|v1_sOjKEbPP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5286703854134161583, 'MsgSeq': 871421475}
2025-08-03 01:04:36 | DEBUG | 从群聊消息中提取发送者: wxid_4183511832012
2025-08-03 01:04:36 | INFO | 收到公众号文章消息: 消息ID:1161938365 来自:***********@chatroom
2025-08-03 01:04:36 | ERROR | 解析XML失败: mismatched tag: line 1, column 147
2025-08-03 01:04:36 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-08-03 01:04:36 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-08-03 01:04:36 | DEBUG | 尝试使用修复后的XML重新解析
2025-08-03 01:04:36 | WARNING | 未能从XML中提取到公众号ID
2025-08-03 01:04:36 | DEBUG | 未提取到公众号ID，跳过处理
2025-08-03 01:05:12 | DEBUG | 收到消息: {'MsgId': 323728078, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_4183511832012:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b3049020100020474e9b25902032f7d6f020472c897240204688e4549042462623434333765622d643465382d343636622d396535342d6434656431393036383837300204051408030201000405004c543f00</cdnthumburl>\n\t\t\t<cdnthumbmd5>2c12f84b42940e477676c56b128fedc1</cdnthumbmd5>\n\t\t\t<cdnthumblength>75205</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>e3b2b5b3559368ceb836e8110cdd847e</cdnthumbaeskey>\n\t\t\t<aeskey>e3b2b5b3559368ceb836e8110cdd847e</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&amp;taskId=</publisherId>\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>2c12f84b42940e477676c56b128fedc1</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&taskId=]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>\n\t\t\t<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>\n\t\t\t<shareId><![CDATA[1_wxa708de63ee4a2353_29913a8ed3ae9af858ca7fdb273e167d_1753779256_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_4183511832012</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754154313, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>f738d72f2bc1b3303178067109208f0e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0umn7IOj|v1_V0aGcHur</signature>\n</msgsource>\n', 'NewMsgId': 7083773185629360597, 'MsgSeq': 871421476}
2025-08-03 01:05:12 | DEBUG | 从群聊消息中提取发送者: wxid_4183511832012
2025-08-03 01:05:12 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des />
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b3049020100020474e9b25902032f7d6f020472c897240204688e4549042462623434333765622d643465382d343636622d396535342d6434656431393036383837300204051408030201000405004c543f00</cdnthumburl>
			<cdnthumbmd5>2c12f84b42940e477676c56b128fedc1</cdnthumbmd5>
			<cdnthumblength>75205</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>e3b2b5b3559368ceb836e8110cdd847e</cdnthumbaeskey>
			<aeskey>e3b2b5b3559368ceb836e8110cdd847e</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&amp;taskId=</publisherId>
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>2c12f84b42940e477676c56b128fedc1</md5>
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[1_wxa708de63ee4a2353_29913a8ed3ae9af858ca7fdb273e167d_1753779256_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_4183511832012</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-03 01:05:12 | DEBUG | XML消息类型: 33
2025-08-03 01:05:12 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 01:05:12 | DEBUG | XML消息描述: None
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumburl: 3057020100044b3049020100020474e9b25902032f7d6f020472c897240204688e4549042462623434333765622d643465382d343636622d396535342d6434656431393036383837300204051408030201000405004c543f00
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumbmd5: 2c12f84b42940e477676c56b128fedc1
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumblength: 75205
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumbheight: 576
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-08-03 01:05:12 | DEBUG | 附件信息 cdnthumbaeskey: e3b2b5b3559368ceb836e8110cdd847e
2025-08-03 01:05:12 | DEBUG | 附件信息 aeskey: e3b2b5b3559368ceb836e8110cdd847e
2025-08-03 01:05:12 | DEBUG | 附件信息 encryver: 1
2025-08-03 01:05:12 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-03 01:05:12 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 01:05:12 | INFO | 未知的XML消息类型: 33
2025-08-03 01:05:12 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 01:05:12 | INFO | 消息描述: None
2025-08-03 01:05:12 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 01:05:12 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des />
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b3049020100020474e9b25902032f7d6f020472c897240204688e4549042462623434333765622d643465382d343636622d396535342d6434656431393036383837300204051408030201000405004c543f00</cdnthumburl>
			<cdnthumbmd5>2c12f84b42940e477676c56b128fedc1</cdnthumbmd5>
			<cdnthumblength>75205</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>e3b2b5b3559368ceb836e8110cdd847e</cdnthumbaeskey>
			<aeskey>e3b2b5b3559368ceb836e8110cdd847e</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&amp;taskId=</publisherId>
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>2c12f84b42940e477676c56b128fedc1</md5>
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81axJc8oeT5TN2Yn35pdngw0&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[1_wxa708de63ee4a2353_29913a8ed3ae9af858ca7fdb273e167d_1753779256_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_4183511832012</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-03 01:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 01:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 01:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 01:39:58 | DEBUG | 收到消息: {'MsgId': 884631968, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n点歌 四次元婚礼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754156399, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_n0Uyryhs|v1_mHNghbZH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹忙 : 点歌 四次元婚礼', 'NewMsgId': 1860849398108520942, 'MsgSeq': 871421477}
2025-08-03 01:39:58 | INFO | 收到文本消息: 消息ID:884631968 来自:55878994168@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:点歌 四次元婚礼
2025-08-03 01:39:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '点歌 四次元婚礼' from wxid_rwfb9vuy93jn22 in 55878994168@chatroom
2025-08-03 01:39:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['点歌', '四次元婚礼']
2025-08-03 01:40:01 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>四次元婚礼 (乙游破防曲)</title><des>小阿漾</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://er-sycdn.kuwo.cn/432d48e48b4a64ed034bf417f3edcde9/688e4d73/resource/30106/trackmedia/F000001ugr0b2eKe0g.flac?src=无损音质 FLAC_8.45 MB?src=320_8.45 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://er-sycdn.kuwo.cn/432d48e48b4a64ed034bf417f3edcde9/688e4d73/resource/30106/trackmedia/F000001ugr0b2eKe0g.flac?src=无损音质 FLAC_8.45 MB?src=320_8.45 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20250312/20250312183816881674.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>[id:$00000000]
[ti:]
[ar:]
[al:]
[by:天琴实验室AI生成v1.0]
[00:00.00]四次元婚礼(乙游破防曲)
[00:01.51]演唱: 小阿漾
[00:03.02]词: 知锦，张晓晨
[00:04.53]曲: 知锦，张晓晨
[00:06.04]编曲: 阿执
[00:07.55]混音: 前奏
[00:09.06]吉他: 阿青
[00:10.57]和声: 白荼
[00:12.08]监制: 知锦
[00:13.59]出品: 情绪出口
[00:15.10]本歌曲商用授权，上音为所益。
[00:16.61]未经著作权人书面许可，不得以任何方式（包括翻唱、翻录等）使用
[00:18.12]第一次吻你是用我眼睛
[00:23.34]微微发烫的手机
[00:26.82]在我颤抖的手心
[00:30.96]你会在屏幕里保持爱意
[00:34.47]却在我生命里永远缺席
[00:38.64]世界像一个巨大的娃娃机
[00:42.18]隔着玻璃只想要你
[00:45.00]能不能走出这相片
[00:47.67]说声久等了抱歉
[00:49.44]在这特别的瞬间
[00:52.44]你落下的眼泪会不会化作雨点
[00:59.01]无数个崩溃失眠的黑夜
[01:02.97]变成有你的第五季节
[01:06.42]梦境倒叙
[01:08.25]我会赴约
[01:10.38]哪怕在另一个世界
[01:42.24]第一次吻你是用我眼睛
[01:47.46]微微发烫的手机
[01:50.91]在我颤抖的手心
[01:55.05]你会在屏幕里保持爱意
[01:58.53]却在我生命里永远缺席
[02:02.73]世界像一个巨大的娃娃机
[02:06.27]隔着玻璃只想要你
[02:09.09]能不能走出这相片
[02:11.76]说声久等了抱歉
[02:13.53]在这特别的瞬间
[02:16.53]你落下的眼泪会不会化作雨点
[02:23.10]无数个崩溃失眠的黑夜
[02:27.06]变成有你的第五季节
[02:30.51]梦境倒叙
[02:32.34]我会赴约
[02:34.47]哪怕在另一个世界
[02:40.56]我这一生冗长苦涩
[02:43.29]唯有你出现那刻
[02:45.09]才知存在是多么值得
[02:48.09]被你改变的那部分代
[02:50.31]替你在我身边
[02:52.02]爱没有偏见
[02:53.91]次元不是界限
[03:01.68]能不能走出这相片
[03:04.32]说声久等了抱歉
[03:06.06]在这特别的瞬间
[03:09.15]你落下的眼泪会不会化作雨点
[03:15.63]无数个崩溃失眠的黑夜
[03:19.62]变成有你的第五季节
[03:23.07]梦境倒叙
[03:24.90]我会赴约
[03:27.00]哪怕在另一个世界
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20250312/20250312183816881674.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-08-03 01:40:01 | DEBUG | 处理消息内容: '点歌 四次元婚礼'
2025-08-03 01:40:01 | DEBUG | 消息内容 '点歌 四次元婚礼' 不匹配任何命令，忽略
2025-08-03 01:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 02:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 02:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 02:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 02:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 03:09:22 | DEBUG | 收到消息: {'MsgId': 1655811123, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des>唱舞星愿站</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>\n\t\t\t<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>\n\t\t\t<cdnthumblength>173353</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>\n\t\t\t<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>69b76813fe0b2a04149aee01978e42f7</md5>\n\t\t<websearch />\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>\n\t\t\t<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t\t<wxaTradeCommentScore>0</wxaTradeCommentScore>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>0</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754161763, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>a584aae0f79bade59009b5c2a08d68fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_i4IS+JJP|v1_gQOoWsWM</signature>\n</msgsource>\n', 'PushContent': '郭 : [小程序]派对邀请函-签到解锁6周年专属称号', 'NewMsgId': 1697720978225700642, 'MsgSeq': 871421480}
2025-08-03 03:09:22 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-03 03:09:22 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
			<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-03 03:09:22 | DEBUG | XML消息类型: 33
2025-08-03 03:09:22 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 03:09:22 | DEBUG | XML消息描述: 唱舞星愿站
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumbmd5: 69b76813fe0b2a04149aee01978e42f7
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumblength: 173353
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumbheight: 576
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-08-03 03:09:22 | DEBUG | 附件信息 cdnthumbaeskey: f10bff006983850491a301a4cdb0c357
2025-08-03 03:09:22 | DEBUG | 附件信息 aeskey: f10bff006983850491a301a4cdb0c357
2025-08-03 03:09:22 | DEBUG | 附件信息 encryver: 1
2025-08-03 03:09:22 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-03 03:09:22 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 03:09:22 | INFO | 未知的XML消息类型: 33
2025-08-03 03:09:22 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 03:09:22 | INFO | 消息描述: 唱舞星愿站
2025-08-03 03:09:22 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 03:09:22 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
			<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-03 03:10:07 | DEBUG | 收到消息: {'MsgId': 904355674, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754161809, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_QNH9nAA+|v1_mK7niYJ3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1548140262252340764, 'MsgSeq': 871421481}
2025-08-03 03:10:07 | INFO | 收到表情消息: 消息ID:904355674 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-08-03 03:10:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1548140262252340764
2025-08-03 03:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 03:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 03:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 03:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 04:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 04:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 04:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 04:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 05:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 05:15:54 | DEBUG | 群成员变化检查完成
2025-08-03 05:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 05:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 06:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 06:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 06:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 06:34:39 | DEBUG | 收到消息: {'MsgId': 1754173860, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025080300</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1754173805</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjuorrEBkDtorrEBkiko7rEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754174082, 'NewMsgId': 1754173860, 'MsgSeq': 0}
2025-08-03 06:34:39 | DEBUG | 系统消息类型: functionmsg
2025-08-03 06:34:39 | INFO | 未知的系统消息类型: {'MsgId': 1754173860, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025080300</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1754173805</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjuorrEBkDtorrEBkiko7rEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754174082, 'NewMsgId': 1754173860, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-08-03 06:36:44 | DEBUG | 收到消息: {'MsgId': 364428330, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754174206, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_KGGYQfsS|v1_JnT8Zu0z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4679727409762495170, 'MsgSeq': 871421482}
2025-08-03 06:36:44 | INFO | 收到文本消息: 消息ID:364428330 来自:***********@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:签到
2025-08-03 06:36:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '签到' from wxid_besewpsontwy29 in ***********@chatroom
2025-08-03 06:36:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['签到']
2025-08-03 06:36:44 | DEBUG | 处理消息内容: '签到'
2025-08-03 06:36:44 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-03 06:36:44 | INFO | 数据库: 用户wxid_besewpsontwy29登录时间设置为2025-08-03 00:00:00+08:00
2025-08-03 06:36:44 | INFO | 数据库: 用户wxid_besewpsontwy29连续签到天数设置为13
2025-08-03 06:36:44 | INFO | 数据库: 用户wxid_besewpsontwy29积分增加10
2025-08-03 06:36:45 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_besewpsontwy29'] 内容:@穆穆 
-----XYBot-----
签到成功！你领到了 8 个积分！✅
你是今天第 2 个签到的！🎉
你连续签到了 13 天！ 再奖励 2 积分！[爱心]
2025-08-03 06:46:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 07:15:52 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-03 07:15:53 | DEBUG | 群成员变化检查完成
2025-08-03 07:16:01 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-03 07:17:57 | DEBUG | 收到消息: {'MsgId': 1975167883, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des>唱舞星愿站</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>\n\t\t\t<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>\n\t\t\t<cdnthumblength>173353</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>\n\t\t\t<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>69b76813fe0b2a04149aee01978e42f7</md5>\n\t\t<websearch />\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>\n\t\t\t<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t\t<wxaTradeCommentScore>0</wxaTradeCommentScore>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>0</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754176674, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>a584aae0f79bade59009b5c2a08d68fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_acAbkosq|v1_z682ChQW</signature>\n</msgsource>\n', 'PushContent': '郭 : [小程序]派对邀请函-签到解锁6周年专属称号', 'NewMsgId': 671796056595701567, 'MsgSeq': 871421485}
2025-08-03 07:17:57 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-03 07:17:57 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
			<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-03 07:17:57 | DEBUG | XML消息类型: 33
2025-08-03 07:17:57 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 07:17:57 | DEBUG | XML消息描述: 唱舞星愿站
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumbmd5: 69b76813fe0b2a04149aee01978e42f7
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumblength: 173353
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumbheight: 576
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-08-03 07:17:57 | DEBUG | 附件信息 cdnthumbaeskey: f10bff006983850491a301a4cdb0c357
2025-08-03 07:17:57 | DEBUG | 附件信息 aeskey: f10bff006983850491a301a4cdb0c357
2025-08-03 07:17:57 | DEBUG | 附件信息 encryver: 1
2025-08-03 07:17:57 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-03 07:17:57 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 07:17:57 | INFO | 未知的XML消息类型: 33
2025-08-03 07:17:57 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-08-03 07:17:57 | INFO | 消息描述: 唱舞星愿站
2025-08-03 07:17:57 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-08-03 07:17:57 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
			<aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

